package builtin

import (
	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/tasks/instock"
	"zeka-go/internal/tasks/message_forward"
	"zeka-go/internal/types"
)

// RegisterBuiltinModules 注册所有内置模块到 TaskLoader
func RegisterBuiltinModules(taskLoader *tasks.TaskLoader, client *types.Client) error {
	logger.Info("📦 注册内置任务模块...")

	// 创建所有内置模块
	modules := []tasks.TaskModule{
		instock.NewInStockTaskModule(),
		message_forward.NewMessageForwardTaskModule(),
	}

	// 获取转发服务用于注入到消息转发模块
	var forwardService types.ForwardRuleManager
	if client != nil && client.Services != nil && client.Services.ForwardService != nil {
		forwardService = client.Services.ForwardService
		logger.Debug("从客户端服务容器获取转发服务")
	}

	// 注册每个模块
	for _, module := range modules {
		// 如果是消息转发模块，需要注入转发服务
		if module.GetName() == "message_forward" && forwardService != nil {
			// 获取模块中的处理器并注入服务
			handlers := module.GetHandlers()
			if forwardHandler, exists := handlers["message_forward"]; exists {
				// 使用接口类型断言，避免导入具体类型
				if msgForwardHandler, ok := forwardHandler.(interface {
					SetForwardService(types.ForwardRuleManager)
				}); ok {
					msgForwardHandler.SetForwardService(forwardService)
					logger.Debug("转发服务已注入到消息转发任务处理器")
				}
			}
		}

		// 注册模块到任务加载器
		if err := taskLoader.RegisterModule(module); err != nil {
			logger.Error("注册任务模块失败", "module", module.GetName(), "error", err)
			// 继续注册其他模块
			continue
		}

		logger.Info("✅ 任务模块注册成功", "module", module.GetName())
	}

	logger.Info("✅ 所有内置任务模块注册完成", "count", len(modules))
	return nil
}
