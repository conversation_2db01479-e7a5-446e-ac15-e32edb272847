package forward

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"zeka-go/internal/services"
	"zeka-go/internal/services/logger"
	"zeka-go/internal/services/template"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
	"github.com/tidwall/gjson"
	"gopkg.in/yaml.v3"
)

// ForwardRuleService 转发规则服务 - 简化重构版本
type ForwardRuleService struct {
	// 基础信息
	name         string
	serviceType  string
	dependencies []string

	// 配置和状态
	configFile string
	config     *types.ForwardRulesConfig
	rules      map[string]*types.ForwardRule
	stats      map[string]*types.ForwardRuleStats
	isRunning  bool

	// 索引
	sourceChannelIndex map[string][]*types.ForwardRule
	targetChannelIndex map[string][]*types.ForwardRule

	// 并发控制
	mu     sync.RWMutex
	ctx    context.Context
	cancel context.CancelFunc

	// 外部依赖（运行时注入）
	queueService     types.QueueService
	mappingService   types.FieldMapper
	filterService    types.FilterEngine
	templateManager  *template.Manager
	templateRenderer *template.Renderer
	channelCache     ChannelNameCacheInterface
}

// ChannelNameCacheInterface 频道名称缓存接口
type ChannelNameCacheInterface interface {
	GetChannelName(channelID string) (string, error)
}

// NewForwardRuleService 创建转发规则服务实例
func NewForwardRuleService(configFile string) *ForwardRuleService {
	ctx, cancel := context.WithCancel(context.Background())

	return &ForwardRuleService{
		name:               "ForwardRuleService",
		serviceType:        "forward",
		dependencies:       []string{"FieldMappingService"},
		configFile:         configFile,
		rules:              make(map[string]*types.ForwardRule),
		sourceChannelIndex: make(map[string][]*types.ForwardRule),
		targetChannelIndex: make(map[string][]*types.ForwardRule),
		stats:              make(map[string]*types.ForwardRuleStats),
		ctx:                ctx,
		cancel:             cancel,
	}
}

// Initialize 初始化服务
func (frs *ForwardRuleService) Initialize(ctx context.Context) error {
	logger.Info("初始化转发规则服务", "config_file", frs.configFile)

	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	if err := frs.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	frs.buildIndexes()

	logger.Info("转发规则服务初始化完成",
		"rules_count", len(frs.rules),
		"enabled_rules", frs.countEnabledRules())

	return nil
}

// Start 启动服务
func (frs *ForwardRuleService) Start(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if frs.isRunning {
		return fmt.Errorf("服务已在运行")
	}

	frs.isRunning = true
	logger.Info("转发规则服务已启动")
	return nil
}

// Stop 停止服务
func (frs *ForwardRuleService) Stop(ctx context.Context) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if !frs.isRunning {
		return fmt.Errorf("服务未运行")
	}

	frs.isRunning = false
	frs.cancel()
	logger.Info("转发规则服务已停止")
	return nil
}

// HealthCheck 健康检查
func (frs *ForwardRuleService) HealthCheck(ctx context.Context) *services.HealthCheckResult {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	result := &services.HealthCheckResult{
		Healthy:   frs.isRunning,
		CheckTime: time.Now(),
	}

	if !frs.isRunning {
		result.Message = "服务未运行"
	} else {
		result.Message = "服务运行正常"
	}

	return result
}

// GetName 获取服务名称
func (frs *ForwardRuleService) GetName() string {
	return frs.name
}

// GetType 获取服务类型
func (frs *ForwardRuleService) GetType() string {
	return frs.serviceType
}

// GetDependencies 获取服务依赖
func (frs *ForwardRuleService) GetDependencies() []string {
	return frs.dependencies
}

// 依赖注入方法
func (frs *ForwardRuleService) SetQueueService(queueService types.QueueService) {
	frs.queueService = queueService
	logger.Debug("队列服务已注入")
}

func (frs *ForwardRuleService) SetMappingService(mappingService types.FieldMapper) {
	frs.mappingService = mappingService
	logger.Debug("字段映射服务已注入")
}

func (frs *ForwardRuleService) SetFilterService(filterService types.FilterEngine) {
	frs.filterService = filterService
	logger.Debug("过滤服务已注入")
}

func (frs *ForwardRuleService) SetChannelCache(channelCache ChannelNameCacheInterface) {
	frs.channelCache = channelCache
	logger.Debug("频道缓存已注入")
}

func (frs *ForwardRuleService) InjectTemplateServices(manager *template.Manager, renderer *template.Renderer) {
	frs.templateManager = manager
	frs.templateRenderer = renderer
	logger.Debug("模板服务已注入")
}

// 核心转发方法
func (frs *ForwardRuleService) ForwardMessage(rule *types.ForwardRule, message interface{}) error {
	if !frs.isRunning {
		return fmt.Errorf("服务未运行")
	}

	// 转换消息格式
	discordMessage, err := frs.convertToDiscordMessage(message)
	if err != nil {
		return fmt.Errorf("消息格式转换失败: %w", err)
	}

	// 处理字段映射
	mappedContent, mappedEmbeds, productItems := frs.processMapping(discordMessage, rule)

	// 应用过滤器
	if frs.filterService != nil && !frs.shouldAllowMessage(mappedContent, mappedEmbeds, rule) {
		logger.Debug("消息被过滤器拦截", "rule", rule.Name)
		return nil
	}

	// 创建并发布转发任务
	task := frs.createSimpleTask(rule, discordMessage, mappedContent, mappedEmbeds, productItems)

	if frs.queueService != nil {
		taskID, err := frs.publishToQueue(task)
		if err != nil {
			return fmt.Errorf("发布任务失败: %w", err)
		}
		logger.Debug("转发任务已发布", "task_id", taskID, "rule", rule.Name)
	}

	// 更新统计
	frs.updateStats(rule.Name, true)

	return nil
}

// GetRulesBySourceChannel 根据源频道获取规则
func (frs *ForwardRuleService) GetRulesBySourceChannel(channelID string) []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	if rules, exists := frs.sourceChannelIndex[channelID]; exists {
		result := make([]*types.ForwardRule, len(rules))
		copy(result, rules)
		return result
	}

	return nil
}

// GetRulesByTargetChannel 根据目标频道获取规则
func (frs *ForwardRuleService) GetRulesByTargetChannel(channelID string) []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	if rules, exists := frs.targetChannelIndex[channelID]; exists {
		result := make([]*types.ForwardRule, len(rules))
		copy(result, rules)
		return result
	}

	return nil
}

// AddRule 添加转发规则
func (frs *ForwardRuleService) AddRule(rule *types.ForwardRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	if _, exists := frs.rules[rule.Name]; exists {
		return fmt.Errorf("规则 %s 已存在", rule.Name)
	}

	frs.rules[rule.Name] = rule
	frs.stats[rule.Name] = &rule.Stats
	frs.config.ForwardRules = append(frs.config.ForwardRules, *rule)
	frs.buildIndexes()

	logger.Info("转发规则已添加", "rule", rule.Name)
	return nil
}

// RemoveRule 移除转发规则
func (frs *ForwardRuleService) RemoveRule(ruleName string) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if _, exists := frs.rules[ruleName]; !exists {
		return fmt.Errorf("规则 %s 不存在", ruleName)
	}

	delete(frs.rules, ruleName)
	delete(frs.stats, ruleName)

	// 从配置中删除
	for i, rule := range frs.config.ForwardRules {
		if rule.Name == ruleName {
			frs.config.ForwardRules = append(frs.config.ForwardRules[:i], frs.config.ForwardRules[i+1:]...)
			break
		}
	}

	frs.buildIndexes()
	logger.Info("转发规则已删除", "rule", ruleName)
	return nil
}

// UpdateRule 更新转发规则
func (frs *ForwardRuleService) UpdateRule(rule *types.ForwardRule) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if err := rule.Validate(); err != nil {
		return fmt.Errorf("规则验证失败: %w", err)
	}

	if _, exists := frs.rules[rule.Name]; !exists {
		return fmt.Errorf("规则 %s 不存在", rule.Name)
	}

	frs.rules[rule.Name] = rule
	frs.stats[rule.Name] = &rule.Stats

	// 更新配置
	for i, configRule := range frs.config.ForwardRules {
		if configRule.Name == rule.Name {
			frs.config.ForwardRules[i] = *rule
			break
		}
	}

	frs.buildIndexes()
	logger.Info("转发规则已更新", "rule", rule.Name)
	return nil
}

// GetRule 获取转发规则
func (frs *ForwardRuleService) GetRule(ruleName string) (*types.ForwardRule, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rule, exists := frs.rules[ruleName]
	if !exists {
		return nil, fmt.Errorf("规则 %s 不存在", ruleName)
	}

	// 返回副本
	ruleCopy := *rule
	return &ruleCopy, nil
}

// ListRules 列出所有转发规则
func (frs *ForwardRuleService) ListRules() []*types.ForwardRule {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	rules := make([]*types.ForwardRule, 0, len(frs.rules))
	for _, rule := range frs.rules {
		ruleCopy := *rule
		rules = append(rules, &ruleCopy)
	}

	return rules
}

// GetRuleStats 获取规则统计信息
func (frs *ForwardRuleService) GetRuleStats(ruleName string) (*types.ForwardRuleStats, error) {
	frs.mu.RLock()
	defer frs.mu.RUnlock()

	stats, exists := frs.stats[ruleName]
	if !exists {
		return nil, fmt.Errorf("规则 %s 的统计信息不存在", ruleName)
	}

	// 返回副本
	statsCopy := *stats
	return &statsCopy, nil
}

// UpdateRuleStats 更新规则统计信息
func (frs *ForwardRuleService) UpdateRuleStats(ruleName string, stats *types.ForwardRuleStats) error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if _, exists := frs.stats[ruleName]; !exists {
		return fmt.Errorf("规则 %s 的统计信息不存在", ruleName)
	}

	frs.stats[ruleName] = stats
	return nil
}

// ShouldForward 检查是否应该转发消息
func (frs *ForwardRuleService) ShouldForward(rule *types.ForwardRule, message interface{}) (bool, error) {
	return rule.Enabled, nil
}

// ReloadConfig 重新加载配置
func (frs *ForwardRuleService) ReloadConfig() error {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	logger.Info("重新加载转发规则配置")

	if err := frs.loadConfig(); err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	if err := frs.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	frs.buildIndexes()

	logger.Info("转发规则配置重新加载完成",
		"rules_count", len(frs.rules),
		"enabled_rules", frs.countEnabledRules())

	return nil
}

// 辅助方法

// loadConfig 加载配置文件
func (frs *ForwardRuleService) loadConfig() error {
	data, err := os.ReadFile(frs.configFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 尝试新格式
	newConfig := &types.ForwardRulesMainConfig{}
	if err := yaml.Unmarshal(data, newConfig); err == nil && newConfig.ForwardRules.Rules != nil {
		frs.config = &types.ForwardRulesConfig{
			ForwardRules:   newConfig.ForwardRules.Rules,
			GlobalSettings: newConfig.GlobalSettings,
		}
	} else {
		// 回退到旧格式
		config := &types.ForwardRulesConfig{}
		if err := yaml.Unmarshal(data, config); err != nil {
			return fmt.Errorf("解析配置文件失败: %w", err)
		}
		frs.config = config
	}

	// 加载规则到内存
	frs.rules = make(map[string]*types.ForwardRule)
	frs.stats = make(map[string]*types.ForwardRuleStats)

	for i := range frs.config.ForwardRules {
		rule := &frs.config.ForwardRules[i]

		// 生成规则名称（如果为空）
		if rule.Name == "" {
			rule.Name = fmt.Sprintf("%s_to_%s", rule.InputChannel, rule.OutputChannel)
		}

		// 同步新旧字段名
		if rule.InputChannel != "" && rule.SourceChannelID == "" {
			rule.SourceChannelID = rule.InputChannel
		}
		if rule.OutputChannel != "" && rule.TargetChannelID == "" {
			rule.TargetChannelID = rule.OutputChannel
		}

		// 验证规则
		if err := rule.Validate(); err != nil {
			logger.Error("规则验证失败，跳过", "rule", rule.Name, "error", err)
			continue
		}

		frs.rules[rule.Name] = rule
		frs.stats[rule.Name] = &rule.Stats
	}

	return nil
}

// validateConfig 验证配置
func (frs *ForwardRuleService) validateConfig() error {
	if frs.config == nil {
		return fmt.Errorf("配置为空")
	}

	if len(frs.config.ForwardRules) == 0 {
		return fmt.Errorf("没有配置转发规则")
	}

	return nil
}

// buildIndexes 构建索引
func (frs *ForwardRuleService) buildIndexes() {
	frs.sourceChannelIndex = make(map[string][]*types.ForwardRule)
	frs.targetChannelIndex = make(map[string][]*types.ForwardRule)

	for _, rule := range frs.rules {
		if rule.Enabled {
			sourceID := rule.GetSourceChannelID()
			targetID := rule.GetTargetChannelID()

			frs.sourceChannelIndex[sourceID] = append(frs.sourceChannelIndex[sourceID], rule)
			frs.targetChannelIndex[targetID] = append(frs.targetChannelIndex[targetID], rule)
		}
	}
}

// countEnabledRules 统计启用的规则数量
func (frs *ForwardRuleService) countEnabledRules() int {
	count := 0
	for _, rule := range frs.rules {
		if rule.Enabled {
			count++
		}
	}
	return count
}

// convertToDiscordMessage 转换消息为Discord消息格式
func (frs *ForwardRuleService) convertToDiscordMessage(message interface{}) (*discordgo.Message, error) {
	switch msg := message.(type) {
	case *discordgo.Message:
		return msg, nil
	case discordgo.Message:
		return &msg, nil
	case *discordgo.MessageCreate:
		// MessageCreate 包含一个 Message 字段
		return msg.Message, nil
	case discordgo.MessageCreate:
		// MessageCreate 包含一个 Message 字段
		return msg.Message, nil
	default:
		return nil, fmt.Errorf("不支持的消息类型: %T", message)
	}
}

// processMapping 处理字段映射（使用FieldMappingService）
func (frs *ForwardRuleService) processMapping(message *discordgo.Message, rule *types.ForwardRule) (string, []*discordgo.MessageEmbed, []*types.ProductItem) {
	if frs.mappingService == nil {
		// 如果没有映射服务，仍然提取基础产品信息
		var allProductItems []*types.ProductItem
		for _, embed := range message.Embeds {
			embedData := frs.convertEmbedToMap(embed)
			extractedData := frs.extractEmbedDataForMapping(embedData)
			productItem := frs.createProductFromExtractedData(extractedData)
			if productItem != nil {
				allProductItems = append(allProductItems, productItem)
			}
		}
		return message.Content, message.Embeds, allProductItems
	}

	// 将Discord消息转换为map格式用于映射
	messageData := frs.convertDiscordMessageToMap(message)

	// 检查是否包含 embeds 字段
	embeds, hasEmbeds := messageData["embeds"].([]map[string]interface{})
	if !hasEmbeds || len(embeds) == 0 {
		return message.Content, message.Embeds, nil
	}

	var allProductItems []*types.ProductItem
	mappedEmbeds := make([]*discordgo.MessageEmbed, 0, len(embeds))

	for _, embedData := range embeds {
		// 使用统一的数据提取流程
		extractedData := frs.extractEmbedDataForMapping(embedData)

		// 根据AuthorName自动选择映射组
		var mappingGroupName string
		if authorName, ok := extractedData["AuthorName"].(string); ok && authorName != "" {
			// 根据AuthorName查找映射组
			foundGroup, err := frs.mappingService.FindMappingGroupByAuthor(authorName)
			if err != nil {
				logger.Warn("根据AuthorName查找映射组失败", "author_name", authorName, "error", err)
				mappingGroupName = rule.FieldMappingGroup
			} else {
				mappingGroupName = foundGroup
				logger.Info("根据AuthorName自动选择映射组",
					"author_name", authorName,
					"selected_group", mappingGroupName,
					"rule_group", rule.FieldMappingGroup)
			}
		} else {
			// 如果没有AuthorName，使用规则中指定的映射组
			mappingGroupName = rule.FieldMappingGroup
		}

		// 如果仍然没有映射组，使用默认映射组
		if mappingGroupName == "" {
			mappingGroupName = "default"
		}

		// 应用字段映射
		mappingResult, err := frs.mappingService.MapFields(extractedData, mappingGroupName)
		if err != nil {
			logger.Error("字段映射失败", "error", err, "rule", rule.Name, "mapping_group", mappingGroupName)
			// 映射失败时，创建基础产品信息
			productItem := frs.createProductFromExtractedData(extractedData)
			if productItem != nil {
				allProductItems = append(allProductItems, productItem)
			}
			mappedEmbeds = append(mappedEmbeds, frs.convertMapToDiscordEmbed(embedData))
		} else {
			// 映射成功，使用映射结果
			if mappingResult.Product != nil {
				allProductItems = append(allProductItems, mappingResult.Product)

				// 记录详细的字段映射信息
				for _, appliedRule := range mappingResult.AppliedRules {
					logger.Info("字段映射详情",
						"source_field", appliedRule.SourceField,
						"target_field", appliedRule.TargetField,
						"matched_field", appliedRule.MatchedField,
						"used_fallback", appliedRule.UsedFallback,
						"fallback_index", appliedRule.FallbackIndex,
						"value", appliedRule.Value,
						"transform", appliedRule.Transform)
				}

				logger.Debug("字段映射成功",
					"rule", rule.Name,
					"mapping_group", mappingGroupName,
					"applied_rules", len(mappingResult.AppliedRules),
					"errors", len(mappingResult.Errors),
					"warnings", len(mappingResult.Warnings))
			}

			mappedEmbeds = append(mappedEmbeds, frs.convertMapToDiscordEmbed(embedData))
		}
	}

	// 生成映射后的内容
	mappedContent := message.Content
	if len(allProductItems) > 0 {
		var contentParts []string
		for _, product := range allProductItems {
			if product.Title != "" {
				contentParts = append(contentParts, product.Title)
			}
		}
		if len(contentParts) > 0 {
			mappedContent = strings.Join(contentParts, "\n")
		}
	}

	return mappedContent, mappedEmbeds, allProductItems
}

// convertDiscordMessageToMap 将Discord消息转换为map格式
func (frs *ForwardRuleService) convertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"content":    message.Content,
		"author_id":  message.Author.ID,
		"author":     message.Author.Username,
		"channel_id": message.ChannelID,
		"guild_id":   message.GuildID,
		"message_id": message.ID,
		"timestamp":  message.Timestamp,
	}

	// 转换embeds
	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embedMap := map[string]interface{}{
				"title":       embed.Title,
				"description": embed.Description,
				"url":         embed.URL,
				"color":       embed.Color,
			}

			// 添加图片信息
			if embed.Image != nil {
				embedMap["image"] = map[string]interface{}{
					"url": embed.Image.URL,
				}
			}

			// 添加缩略图信息
			if embed.Thumbnail != nil {
				embedMap["thumbnail"] = map[string]interface{}{
					"url": embed.Thumbnail.URL,
				}
			}

			// 添加作者信息
			if embed.Author != nil {
				authorMap := make(map[string]interface{})
				if embed.Author.Name != "" {
					authorMap["name"] = embed.Author.Name
				}
				if embed.Author.URL != "" {
					authorMap["url"] = embed.Author.URL
				}
				if embed.Author.IconURL != "" {
					authorMap["icon_url"] = embed.Author.IconURL
				}
				if len(authorMap) > 0 {
					embedMap["author"] = authorMap
				}
			}

			// 添加页脚信息
			if embed.Footer != nil {
				footerMap := make(map[string]interface{})
				if embed.Footer.Text != "" {
					footerMap["text"] = embed.Footer.Text
				}
				if embed.Footer.IconURL != "" {
					footerMap["icon_url"] = embed.Footer.IconURL
				}
				if len(footerMap) > 0 {
					embedMap["footer"] = footerMap
				}
			}

			// 添加时间戳
			if embed.Timestamp != "" {
				embedMap["timestamp"] = embed.Timestamp
			}

			// 添加字段信息
			if embed.Fields != nil {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedMap["fields"] = fields
			}

			embeds[i] = embedMap
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// extractEmbedDataForMapping 统一的embed数据提取方法（使用gjson）
// 将embed数据提取为标准化的map，键名与ProductItem字段名一致
func (frs *ForwardRuleService) extractEmbedDataForMapping(embedData map[string]interface{}) map[string]interface{} {
	extractedData := make(map[string]interface{})

	// 将embedData转换为JSON字符串，以便使用gjson
	jsonData, err := json.Marshal(embedData)
	if err != nil {
		logger.Error("转换embed数据为JSON失败", "error", err)
		return extractedData
	}
	jsonStr := string(jsonData)

	// 使用gjson提取基础字段，键名与ProductItem字段名一致
	if title := gjson.Get(jsonStr, "title").String(); title != "" {
		extractedData["Title"] = title
	}

	if description := gjson.Get(jsonStr, "description").String(); description != "" {
		extractedData["Description"] = description
	}

	if url := gjson.Get(jsonStr, "url").String(); url != "" {
		extractedData["URL"] = url
	}

	if color := gjson.Get(jsonStr, "color"); color.Exists() {
		extractedData["Color"] = color.Int()
	}

	// 提取图片信息
	if imageURL := gjson.Get(jsonStr, "image.url").String(); imageURL != "" {
		extractedData["ImageURL"] = imageURL
	}

	if thumbnailURL := gjson.Get(jsonStr, "thumbnail.url").String(); thumbnailURL != "" {
		extractedData["ThumbnailURL"] = thumbnailURL
	}

	// 提取作者信息
	if authorName := gjson.Get(jsonStr, "author.name").String(); authorName != "" {
		extractedData["AuthorName"] = authorName
	}

	if authorURL := gjson.Get(jsonStr, "author.url").String(); authorURL != "" {
		extractedData["AuthorURL"] = authorURL
	}

	if authorIconURL := gjson.Get(jsonStr, "author.icon_url").String(); authorIconURL != "" {
		extractedData["AuthorIconURL"] = authorIconURL
	}

	// 提取页脚信息
	if footerText := gjson.Get(jsonStr, "footer.text").String(); footerText != "" {
		extractedData["FooterText"] = footerText
	}

	if footerIconURL := gjson.Get(jsonStr, "footer.icon_url").String(); footerIconURL != "" {
		extractedData["FooterIconURL"] = footerIconURL
	}

	// 提取时间戳
	if timestamp := gjson.Get(jsonStr, "timestamp").String(); timestamp != "" {
		extractedData["Timestamp"] = timestamp
	}

	// 使用gjson处理fields字段，将其展开为键值对（保持原始字段名，合并重复字段）
	fieldsResult := gjson.Get(jsonStr, "fields")
	if fieldsResult.Exists() && fieldsResult.IsArray() {
		logger.Info("开始处理fields字段", "fields_count", len(fieldsResult.Array()))

		// 用于跟踪重复字段
		fieldCounts := make(map[string]int)

		fieldsResult.ForEach(func(key, value gjson.Result) bool {
			name := value.Get("name").String()
			fieldValue := value.Get("value").String()

			if name != "" && fieldValue != "" {
				// 检查是否已存在相同字段名
				if existingValue, exists := extractedData[name]; exists {
					// 字段已存在，合并值
					fieldCounts[name]++

					// 将现有值转换为字符串
					existingStr := fmt.Sprintf("%v", existingValue)

					// 合并值，使用分隔符
					mergedValue := existingStr + " | " + fieldValue
					extractedData[name] = mergedValue

					logger.Info("字段合并成功",
						"field_name", name,
						"existing_value", existingStr,
						"new_value", fieldValue,
						"merged_value", mergedValue,
						"occurrence_count", fieldCounts[name]+1)
				} else {
					// 首次出现的字段
					extractedData[name] = fieldValue
					fieldCounts[name] = 0

					logger.Info("字段提取成功",
						"field_name", name,
						"value", fieldValue)
				}
			}

			return true // 继续遍历
		})

		// 记录重复字段统计
		duplicateCount := 0
		for fieldName, count := range fieldCounts {
			if count > 0 {
				duplicateCount++
				logger.Info("发现重复字段",
					"field_name", fieldName,
					"total_occurrences", count+1)
			}
		}

		if duplicateCount > 0 {
			logger.Info("字段合并统计",
				"total_fields", len(fieldCounts),
				"duplicate_fields", duplicateCount)
		}
	}

	logger.Info("embed数据提取完成", "extracted_fields", len(extractedData))
	return extractedData
}

// createProductFromExtractedData 从提取的数据创建ProductItem（fallback方法）
func (frs *ForwardRuleService) createProductFromExtractedData(extractedData map[string]interface{}) *types.ProductItem {
	product := &types.ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	// 从提取的数据中设置基础字段
	if title, ok := extractedData["Title"].(string); ok && title != "" {
		product.Title = title
	}

	if url, ok := extractedData["URL"].(string); ok && url != "" {
		product.URL = url
	}

	if productID, ok := extractedData["ProductID"].(string); ok && productID != "" {
		product.ProductID = productID
	}

	if price, ok := extractedData["Price"].(string); ok && price != "" {
		product.Price = price
	}

	// 如果没有基本信息，返回nil
	if product.Title == "" && product.URL == "" && product.ProductID == "" {
		return nil
	}

	return product
}

// convertEmbedToMap 将单个Discord embed转换为map格式
func (frs *ForwardRuleService) convertEmbedToMap(embed *discordgo.MessageEmbed) map[string]interface{} {
	embedMap := map[string]interface{}{
		"title":       embed.Title,
		"description": embed.Description,
		"url":         embed.URL,
		"color":       embed.Color,
	}

	// 添加图片信息
	if embed.Image != nil {
		embedMap["image"] = map[string]interface{}{
			"url": embed.Image.URL,
		}
	}

	// 添加缩略图信息
	if embed.Thumbnail != nil {
		embedMap["thumbnail"] = map[string]interface{}{
			"url": embed.Thumbnail.URL,
		}
	}

	// 添加作者信息
	if embed.Author != nil {
		authorMap := make(map[string]interface{})
		if embed.Author.Name != "" {
			authorMap["name"] = embed.Author.Name
		}
		if embed.Author.URL != "" {
			authorMap["url"] = embed.Author.URL
		}
		if embed.Author.IconURL != "" {
			authorMap["icon_url"] = embed.Author.IconURL
		}
		if len(authorMap) > 0 {
			embedMap["author"] = authorMap
		}
	}

	// 添加页脚信息
	if embed.Footer != nil {
		footerMap := make(map[string]interface{})
		if embed.Footer.Text != "" {
			footerMap["text"] = embed.Footer.Text
		}
		if embed.Footer.IconURL != "" {
			footerMap["icon_url"] = embed.Footer.IconURL
		}
		if len(footerMap) > 0 {
			embedMap["footer"] = footerMap
		}
	}

	// 添加时间戳
	if embed.Timestamp != "" {
		embedMap["timestamp"] = embed.Timestamp
	}

	// 添加字段信息
	if embed.Fields != nil {
		fields := make([]map[string]interface{}, len(embed.Fields))
		for j, field := range embed.Fields {
			fields[j] = map[string]interface{}{
				"name":   field.Name,
				"value":  field.Value,
				"inline": field.Inline,
			}
		}
		embedMap["fields"] = fields
	}

	return embedMap
}

// convertMapToDiscordEmbed 将map转换为Discord embed
func (frs *ForwardRuleService) convertMapToDiscordEmbed(embedData map[string]interface{}) *discordgo.MessageEmbed {
	embed := &discordgo.MessageEmbed{}

	if title, ok := embedData["title"].(string); ok {
		embed.Title = title
	}

	if description, ok := embedData["description"].(string); ok {
		embed.Description = description
	}

	if url, ok := embedData["url"].(string); ok {
		embed.URL = url
	}

	if color, ok := embedData["color"].(int); ok {
		embed.Color = color
	}

	// 处理图片
	if image, ok := embedData["image"].(map[string]interface{}); ok {
		if imageURL, ok := image["url"].(string); ok {
			embed.Image = &discordgo.MessageEmbedImage{URL: imageURL}
		}
	}

	// 处理缩略图
	if thumbnail, ok := embedData["thumbnail"].(map[string]interface{}); ok {
		if thumbnailURL, ok := thumbnail["url"].(string); ok {
			embed.Thumbnail = &discordgo.MessageEmbedThumbnail{URL: thumbnailURL}
		}
	}

	// 处理字段
	if fields, ok := embedData["fields"].([]map[string]interface{}); ok {
		embed.Fields = make([]*discordgo.MessageEmbedField, len(fields))
		for i, field := range fields {
			embedField := &discordgo.MessageEmbedField{}
			if name, ok := field["name"].(string); ok {
				embedField.Name = name
			}
			if value, ok := field["value"].(string); ok {
				embedField.Value = value
			}
			if inline, ok := field["inline"].(bool); ok {
				embedField.Inline = inline
			}
			embed.Fields[i] = embedField
		}
	}

	return embed
}

// shouldAllowMessage 检查消息是否应该被允许
func (frs *ForwardRuleService) shouldAllowMessage(content string, embeds []*discordgo.MessageEmbed, rule *types.ForwardRule) bool {
	if frs.filterService == nil {
		return true
	}

	// 检查内容过滤
	if content != "" {
		result, err := frs.filterService.CheckMessage(rule.GetTargetChannelID(), content)
		if err != nil {
			logger.Error("过滤器检查失败", "error", err, "rule", rule.Name)
			return true // 过滤器错误时允许通过
		}
		if !result.Allowed {
			logger.Debug("消息被内容过滤器拦截", "rule", rule.Name, "reason", result.Reason)
			return false
		}
	}

	// 检查embed过滤
	for _, embed := range embeds {
		embedContent := fmt.Sprintf("%s %s", embed.Title, embed.Description)
		if embedContent != "" {
			result, err := frs.filterService.CheckMessage(rule.GetTargetChannelID(), embedContent)
			if err != nil {
				logger.Error("embed过滤器检查失败", "error", err, "rule", rule.Name)
				continue
			}
			if !result.Allowed {
				logger.Debug("embed被过滤器拦截", "rule", rule.Name, "reason", result.Reason)
				return false
			}
		}
	}

	return true
}

// createSimpleTask 创建简单的转发任务
func (frs *ForwardRuleService) createSimpleTask(rule *types.ForwardRule, message *discordgo.Message, content string, embeds []*discordgo.MessageEmbed, products []*types.ProductItem) map[string]interface{} {
	// 创建符合MessageForwardTask格式的任务
	task := map[string]interface{}{
		"id":               fmt.Sprintf("forward_%d", time.Now().UnixNano()),
		"rule_name":        rule.Name,
		"source_channel":   rule.GetSourceChannelID(),
		"target_channels":  []string{rule.GetTargetChannelID()},
		"original_message": content,
		"author_id":        message.Author.ID,
		"guild_id":         message.GuildID,
		"original_id":      message.ID,
		"timestamp":        time.Now().Unix(),
		"delay_seconds":    rule.ForwardConfig.DelaySeconds,
		"mapping_name":     rule.FieldMappingGroup,
		"created_at":       time.Now().Format(time.RFC3339),
	}

	// 添加embeds信息
	if len(embeds) > 0 {
		embedsData := make([]map[string]interface{}, len(embeds))
		for i, embed := range embeds {
			embedData := map[string]interface{}{
				"title":       embed.Title,
				"description": embed.Description,
				"url":         embed.URL,
				"color":       embed.Color,
			}

			if embed.Image != nil {
				embedData["image"] = map[string]interface{}{
					"url": embed.Image.URL,
				}
			}

			if embed.Thumbnail != nil {
				embedData["thumbnail"] = map[string]interface{}{
					"url": embed.Thumbnail.URL,
				}
			}

			if embed.Fields != nil {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedData["fields"] = fields
			}

			embedsData[i] = embedData
		}
		task["embeds"] = embedsData
	}

	// 添加产品信息
	if len(products) > 0 {
		productsData := make([]map[string]interface{}, len(products))
		for i, product := range products {
			productsData[i] = product.ToLegacyProductData()
		}
		task["products"] = productsData
	}

	return task
}

// publishToQueue 发布任务到队列
func (frs *ForwardRuleService) publishToQueue(task map[string]interface{}) (string, error) {
	if frs.queueService == nil {
		return "", fmt.Errorf("队列服务未配置")
	}

	// 使用队列服务发布任务
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	taskID, err := frs.queueService.PublishTask(ctx, "message_forward", "message_forward", task, types.PublishOptions{
		Priority:  0,
		TTL:       24 * time.Hour, // 24小时过期
		Mandatory: false,
		Immediate: false,
	})

	if err != nil {
		return "", fmt.Errorf("发布任务到队列失败: %w", err)
	}

	return taskID, nil
}

// updateStats 更新统计信息
func (frs *ForwardRuleService) updateStats(ruleName string, success bool) {
	frs.mu.Lock()
	defer frs.mu.Unlock()

	if stats, exists := frs.stats[ruleName]; exists {
		if success {
			stats.ForwardedMessages++
		} else {
			stats.ErrorMessages++
		}
		stats.LastForwardTime = time.Now()
	}
}
